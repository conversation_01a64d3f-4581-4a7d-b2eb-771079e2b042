"use client";

import { cn } from "@/lib/utils";
import { <PERSON><PERSON><PERSON>ontainer, LoadingSpinner } from "./loading-spinner";
import { Skeleton, SkeletonCard, SkeletonList, SkeletonDesktopLayout } from "./skeleton";
import { useMediaQuery } from "@/hooks/use-media-query";
import { useSidebar } from "@/contexts/sidebar-context";
import { useChatSidebar } from "@/contexts/chat-sidebar-context";

interface LoadingBoundaryProps {
  isLoading: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  variant?: "spinner" | "skeleton";
  loadingText?: string;
  error?: Error | null;
  errorFallback?: React.ReactNode;
}

export function LoadingBoundary({
  isLoading,
  children,
  fallback,
  className,
  variant = "spinner",
  loadingText = "Loading...",
  error,
  errorFallback
}: LoadingBoundaryProps) {
  // Error state
  if (error) {
    if (errorFallback) {
      return <>{errorFallback}</>;
    }
    
    return (
      <div className={cn("flex flex-col items-center justify-center py-8 text-center", className)}>
        <div className="text-destructive mb-2">⚠️</div>
        <p className="text-sm text-muted-foreground">
          Something went wrong. Please try again.
        </p>
      </div>
    );
  }

  // Loading state
  if (isLoading) {
    if (fallback) {
      return <>{fallback}</>;
    }

    if (variant === "skeleton") {
      return (
        <div className={cn("animate-in fade-in-0 duration-300", className)}>
          <SkeletonList items={3} />
        </div>
      );
    }

    return (
      <div className={cn("animate-in fade-in-0 duration-300", className)}>
        <LoadingContainer text={loadingText} />
      </div>
    );
  }

  // Success state with smooth transition
  return (
    <div className={cn("animate-in fade-in-0 duration-300", className)}>
      {children}
    </div>
  );
}

interface PageLoadingBoundaryProps {
  isLoading: boolean;
  children: React.ReactNode;
  title?: string;
  className?: string;
}

export function PageLoadingBoundary({
  isLoading,
  children,
  title,
  className
}: PageLoadingBoundaryProps) {
  if (isLoading) {
    return (
      <div className={cn("px-2 md:px-4 py-6 space-y-6", className)}>
        {title && (
          <div className="space-y-2">
            <Skeleton className="h-8 w-48" />
            <Skeleton className="h-4 w-64" />
          </div>
        )}
        <div className="grid gap-4">
          <div className="gradient-border-muted rounded-lg p-6">
            <SkeletonCard />
          </div>
          <div className="gradient-border-muted rounded-lg p-6">
            <SkeletonCard />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("animate-in fade-in-0 duration-300", className)}>
      {children}
    </div>
  );
}

interface QueryLoadingBoundaryProps {
  queries: Array<{ isLoading: boolean; error?: Error | null }>;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  className?: string;
  loadingText?: string;
}

export function QueryLoadingBoundary({
  queries,
  children,
  fallback,
  className,
  loadingText = "Loading..."
}: QueryLoadingBoundaryProps) {
  const isLoading = queries.some(query => query.isLoading);
  const error = queries.find(query => query.error)?.error;

  return (
    <LoadingBoundary
      isLoading={isLoading}
      error={error}
      fallback={fallback}
      className={className}
      loadingText={loadingText}
    >
      {children}
    </LoadingBoundary>
  );
}

interface InlineLoadingProps {
  isLoading: boolean;
  size?: "sm" | "md" | "lg";
  text?: string;
  className?: string;
}

export function InlineLoading({
  isLoading,
  size = "sm",
  text,
  className
}: InlineLoadingProps) {
  if (!isLoading) return null;

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <LoadingSpinner size={size} />
      {text && (
        <span className="text-sm text-muted-foreground font-light">
          {text}
        </span>
      )}
    </div>
  );
}

interface DesktopPageLoadingBoundaryProps {
  isLoading: boolean;
  children: React.ReactNode;
  className?: string;
}

export function DesktopPageLoadingBoundary({
  isLoading,
  children,
  className
}: DesktopPageLoadingBoundaryProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const { isCollapsed } = useSidebar();
  const { isOpen: isChatOpen, width: chatWidth } = useChatSidebar();

  if (isLoading && isDesktop) {
    return (
      <SkeletonDesktopLayout
        isCollapsed={isCollapsed}
        isChatOpen={isChatOpen}
        chatWidth={chatWidth}
        className={className}
      />
    );
  }

  if (isLoading) {
    // Mobile loading fallback
    return (
      <div className={cn("px-2 md:px-4 py-6 space-y-6", className)}>
        <div className="grid gap-4">
          <div className="gradient-border-muted rounded-lg p-6">
            <SkeletonCard />
          </div>
          <div className="gradient-border-muted rounded-lg p-6">
            <SkeletonCard />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("animate-in fade-in-0 duration-300", className)}>
      {children}
    </div>
  );
}
